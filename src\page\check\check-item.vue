<template>
    <div class="check-item">
        <div class="c1">
            <h2>{{ keyNum }}.{{ checkData.checkTip }}</h2>
            <ul class="c1ul">
                <li>
                    <v-radio
                        class="radio"
                        v-model="val"
                        value="1"
                        @change="change"
                        ><span>是</span></v-radio
                    >
                </li>
                <li>
                    <v-radio
                        class="radio"
                        v-model="val"
                        value="0"
                        @change="change"
                        ><span>否</span></v-radio
                    >
                </li>
            </ul>
        </div>
        <div class="c2" v-show="showPicList">
            <h2>您可以拍下相关照片</h2>
            <ul>
                <li v-for="(data, index) in checkPhotos" :key="'i' + index">
                    <!-- 视频文件：显示默认缩略图和播放按钮 -->
                    <template v-if="data.imgFileId && data.imgFileUrl">
                        <img
                            class="i1"
                            :src="data.imgFileUrl"
                            alt=""
                            data-who="video"
                        />
                        <div
                            class="i1-play"
                            @click="scanVideo(data.fileUrl, data.imgFileUrl)"
                        ></div>
                    </template>
                    <!-- 普通图片 -->
                    <img
                        class="i1"
                        :src="data.fileUrl"
                        alt=""
                        v-else
                        data-who="img"
                    />
                    <span @click="deleteImg(index)"></span>
                </li>
                <li v-if="checkPhotos && checkPhotos.length < 3">
                    <label @click="SelectImgOrVideoFn"> </label>
                </li>
            </ul>
        </div>
        <div class="select-two" v-show="showSlect">
            <div class="st-mask" @click="closeSelectImg"></div>
            <div class="st-content">
                <div class="st-i">
                    <!-- <div class="st-ic" @click="useCameraFn">
                        <label :for="keyword">
                            <input
                                type="file"
                                :id="keyword"
                                accept="image/*"
                                @change="clickUpload($event)"
                                v-if="!isiOS"
                            />
                            <input
                                type="file"
                                :id="keyword"
                                accept="image/*"
                                @change="clickUpload($event)"
                                v-else
                            />
                        </label>
                        <p class="p1">图片</p>
                    </div> -->
                    <div class="st-ic" @click="YSSC">
                        <img
                            src="../../assets/img/<EMAIL>"
                            alt=""
                        />
                        <p class="p1">图片</p>
                    </div>
                    <div class="st-ic st-icrt" @click="useVideoFn">
                        <img src="../../assets/img/<EMAIL>" alt="" />
                        <p class="p2">视频</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="vi" v-show="showVideo">
            <div class="vi-mask"></div>
            <div class="vi-content">
                <div class="vic-bg" @click="closeVideoBox"></div>
                <div class="vi-main">
                    <!-- 视频预览 -->
                    <div v-if="currentVideoUrl" class="video-preview-container">
                        <video
                            ref="video"
                            :poster="currentVideoPoster"
                            controls
                            preload="metadata"
                            playsinline
                            webkit-playsinline
                            x5-video-player-type="h5"
                            x5-video-player-fullscreen="true"
                            x5-video-orientation="portraint"
                            @error="handleVideoError"
                            @loadedmetadata="handleVideoLoadedMetadata"
                        >
                            <source :src="currentVideoUrl" type="video/mp4">
                            <source :src="currentVideoUrl" type="video/quicktime">
                            <source :src="currentVideoUrl" type="video/mov">
                            <source :src="currentVideoUrl">
                        </video>

                        <!-- 如果视频无法播放，显示提示信息 -->
                        <div v-if="videoLoadError" class="video-error-fallback">
                            <div class="error-icon">📹</div>
                            <p>视频格式暂不支持预览</p>
                            <p class="error-detail">{{ videoErrorMessage }}</p>
                            <a :href="currentVideoUrl" target="_blank" class="download-link">
                                点击查看原文件
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import vRadio from "../../components/v-radio/radio";
import lrz from "lrz";
import { initCookie, upload, getFileToken } from "../../api/check";
import { closeWebview, selectVideo } from "@/assets/js/until.js";
// import 'dplayer/dist/DPlayer.min.css';
import DPlayer from "dplayer";
import defaultVideoThumb from '../../assets/img/video.jpg';
export default {
    props: ["checkData", "keyword", "checkId", "isModified", "keyNum"],
    data() {
        return {
            // 1:是， 0：否，要传图片, null: 不选择
            val: null,
            // fileId 的数组
            checkPhotos: [],
            // 判断是否按钮显示上传图片
            showPicList: false,
            isiOS: false,
            // 显示（图片和视频的）选择项
            showSlect: false,
            // 显示视频
            showVideo: false,
            // 当前播放的视频URL
            currentVideoUrl: "",
            // 当前视频海报图
            currentVideoPoster: "",
            // 视频加载错误状态
            videoLoadError: false,
            // 视频错误信息
            videoErrorMessage: "",
        };
    },
    created() {
        // 判断移动终端
        var u = navigator.userAgent;
        this.isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    mounted() {
        this.$loading(false, "");
        this.draftsBox();
        // console.log('check-item checkData', this.checkData);
    },
    methods: {
        testInit(callback) {
            initCookie({ authCode: "11111111" }).then((result) => {
                console.log("initCookie", result);
                if (result.errorCode === "0") {
                    callback();
                }
            });
        },
        // 初始化，网页版不需要认证，直接执行回调
        initCookiefn(callback) {
            // 网页版不需要认证，直接执行业务逻辑
            if (callback && typeof callback === 'function') {
                callback();
            }
        },
        change(v) {
            // console.log('v=', v);
        },
        YSSC() {
            // H5图片选择
            const _this = this;
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true; // 允许多选

            input.onchange = (e) => {
                const files = e.target.files;
                if (!files.length) return;

                _this.$loading(true, "正在上传图片...");

                // 处理所有选中的图片
                let uploadPromises = [];
                for (let i = 0; i < files.length; i++) {
                    uploadPromises.push(_this.uploadSingleImage(files[i]));
                }

                Promise.all(uploadPromises).then(() => {
                    _this.$loading(false, "");
                    _this.showSlect = false;
                    _this.$toast.center('图片上传成功');
                }).catch((error) => {
                    console.error('图片上传失败', error);
                    _this.$loading(false, "");
                    _this.showSlect = false;
                    _this.$toast.center('部分图片上传失败');
                });
            };

            input.click();
        },
        // 上传单张图片
        uploadSingleImage(file) {
            const _this = this;
            return new Promise((resolve, reject) => {
                // 使用 lrz 压缩图片
                lrz(file, {
                    width: 1000,
                    quality: 0.5,
                }).then(function (rst) {
                    return getFileToken();
                }).then((token) => {
                    if (token.errorCode.toString() !== "0") {
                        reject(new Error('获取token失败'));
                        return;
                    }

                    const fd = new FormData();
                    fd.append("file", file);
                    fd.append("fileToken", token.data);

                    return upload(fd);
                }).then((res) => {
                    if (res.errorCode.toString() === "0") {
                        // 图片上传成功，设置为图片格式（不设置imgFileId和imgFileUrl）
                        _this.checkPhotos.push({
                            fileUrl: res.data.fileUrl,
                            fileId: res.data.fileId,
                            // 图片不设置 imgFileId 和 imgFileUrl，这样模板会显示为图片
                        });
                        console.log('📷 图片上传成功:', {
                            fileId: res.data.fileId,
                            fileUrl: res.data.fileUrl,
                            类型: '图片'
                        });
                        resolve(res);
                    } else {
                        reject(new Error(res.value));
                    }
                }).catch((error) => {
                    console.error('单张图片上传失败', error);
                    reject(error);
                });
            });
        },
        clickUpload(e) {
            const _this = this;
            _this.$loading(true, "");
            var files = e.target.files || e.dataTransfer.files;
            // console.log('files', files);
            if (!files.length) return;
            lrz(files[0], {
                width: 1000,
                quality: 0.5,
            }).then(function (rst) {
                // _this.imgList.push(rst.base64);
                _this.uploadFn(rst.file);
                return rst;
            });
        },
        deleteImg(i) {
            // this.imgList.splice(i, 1);
            this.checkPhotos.splice(i, 1);
        },
        // 上传图片
        uploadFn(file) {
            const _this = this;
            getFileToken()
                .then((token) => {
                    console.log("getFileToken=", token);
                    if (token.errorCode.toString() === "0") {
                        const fd = new FormData();
                        fd.append("file", file);
                        fd.append("fileToken", token.data);
                        upload(fd)
                            .then((res) => {
                                console.log("upload res=", res);
                                _this.$loading(false, "");
                                if (res.errorCode.toString() === "0") {
                                    _this.checkPhotos.push({
                                        fileUrl: res.data.fileUrl,
                                        fileId: res.data.fileId,
                                    });
                                } else {
                                    _this.$toast.center(res.value);
                                }
                            })
                            .catch((error) => {
                                _this.$toast.center("接口异常");
                            });
                    } else if (token.errorCode.toString() === "1003") {
                        _this.initCookiefn(_this.uploadFn);
                        // _this.testInit(_this.uploadFn)
                    } else {
                        _this.$toast.center(token.value);
                    }
                })
                .catch((error) => {
                    _this.$toast.center("接口异常");
                });
        },
        // 选择上传 视频 或 图片
        SelectImgOrVideoFn() {
            this.showSlect = true;
        },
        closeSelectImg() {
            this.showSlect = false;
        },
        useCameraFn() {
            // 关闭弹窗
            this.showSlect = false;
        },
        /*
         * H5选择视频并上传
         */
        async useVideoFn() {
            const _this = this;
            try {
                // 使用H5选择视频
                const videoData = await selectVideo();
                console.log('选择视频成功', videoData);

                _this.$loading(true, '正在上传视频...');

                // 获取上传token
                const token = await getFileToken();
                if (token.errorCode.toString() !== "0") {
                    if (token.errorCode.toString() === "1003") {
                        _this.initCookiefn(_this.useVideoFn);
                    } else {
                        _this.$toast.center(token.value);
                    }
                    _this.$loading(false, '');
                    return;
                }

                // 构建上传FormData
                const formData = new FormData();
                formData.append('file', videoData.file);
                formData.append('fileToken', token.data);

                // 上传视频
                let saveUrl = window.location.origin + "/medical-supervision/file/uploadVedio";

                const response = await fetch(saveUrl, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.errorCode === '0' || result.errorCode === 0) {
                    // 检查返回数据的有效性 - 视频上传接口返回格式为 data.fileInfo
                    if (!result.data || !result.data.fileInfo) {
                        _this.$toast.center('视频上传失败：服务器返回数据无效');
                        _this.$loading(false, '');
                        _this.showSlect = false;
                        return;
                    }

                    const fileInfo = result.data.fileInfo;
                    const fileId = fileInfo.fileId;
                    const fileUrl = fileInfo.fileUrl;

                    if (!fileId || !fileUrl) {
                        console.error('❌ 视频上传接口返回的文件信息不完整:', {
                            fileId: fileId,
                            fileUrl: fileUrl,
                            fileInfo: fileInfo
                        });
                        _this.$toast.center('视频上传失败：文件信息不完整');
                        _this.$loading(false, '');
                        _this.showSlect = false;
                        return;
                    }

                    // 直接使用后端返回的字段，统一使用默认缩略图
                    const videoData = {
                        fileUrl: fileUrl,
                        fileId: fileId,
                        imgFileId: -1, // 统一使用默认缩略图标识
                        imgFileUrl: defaultVideoThumb
                    };

                    console.log('🎥 视频上传成功，使用默认缩略图:', {
                        fileId: fileId,
                        fileUrl: fileUrl,
                        defaultThumb: defaultVideoThumb
                    });

                    console.log('📋 添加视频前的checkPhotos:', _this.checkPhotos.slice());

                    // 添加到照片列表
                    _this.checkPhotos.push(videoData);
                    // 如果用户还没有选择"是"或"否"，给出明确提示
                    if (_this.val === null) {
                        _this.$toast.center('视频上传成功，请选择"是"或"否"');
                    }

                    _this.$toast.center('视频上传成功');
                } else {
                    _this.$toast.center(result.value || '上传失败');
                }

                _this.$loading(false, '');
                _this.showSlect = false;
            } catch (error) {
                console.error('视频选择或上传失败', error);
                _this.$loading(false, '');
                _this.showSlect = false;
                if (error.message !== '用户取消选择') {
                    _this.$toast.center('视频处理失败');
                }
            }
        },
        // 草稿箱修改
        draftsBox() {
            // console.log('this.isModified=========', this.isModified);
            if (this.isModified) {
                this.val = this.checkData.checkStatus;
                if (this.checkData.checkPhotos !== "") {
                    const checkPhotos = JSON.parse(this.checkData.checkPhotos);

                    if (checkPhotos && checkPhotos.length > 0) {
                        this.checkPhotos = checkPhotos.map((file) => {
                            // 区分图片和视频：通过文件扩展名判断
                            let fileData;
                            const fileUrl = file.fileUrl || '';
                            const isVideo = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(fileUrl);

                            if (file.imgFileId !== undefined || isVideo) {
                                // 视频文件：使用默认缩略图
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl,
                                    imgFileId: -1, // 视频使用默认缩略图标识
                                    imgFileUrl: defaultVideoThumb
                                };
                            } else {
                                // 图片文件：不设置imgFileId和imgFileUrl
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl
                                    // 图片不设置 imgFileId 和 imgFileUrl
                                };
                            }

                            return fileData;
                        });
                    } else {
                        this.checkPhotos = [];
                    }
                }
            }
        },
        // 切换radio获取数据
        switchRadioGetDataFn(radioVal) {
            const _this = this;
            let array0 = [];
            const getArr0 = _this.$store.getters.getUploadImgs;
            // console.log('getArr0', getArr0);
            if (getArr0.length > 0) {
                getArr0.forEach((el) => {
                    if (
                        _this.checkData.checkId === el.checkId &&
                        _this.checkData.listId === el.listId &&
                        radioVal === el.checkStatus
                    ) {
                        // 获取图片地址
                        _this.checkPhotos = getArr0.checkPhotos;
                    }
                });
            } else {
                _this.checkPhotos = [];
            }
        },
        scanVideo(v, i) {
            console.log("预览视频 - 原始URL参数=", v);

            // 处理视频URL：检查是否已经是完整URL
            let videoUrl = "";
            if (v.startsWith("http://") || v.startsWith("https://")) {
                // 已经是完整URL，直接使用
                videoUrl = v;
            } else {
                // 相对路径，需要拼接域名
                // 确保路径开头有斜杠
                const path = v.startsWith("/") ? v : "/" + v;
                videoUrl = "https://mss.hfi-health.com:9443" + path;
            }

            console.log("预览视频 - 最终URL=", videoUrl);

            // 使用简单的video标签预览，而不是跳转到外部链接
            this.currentVideoUrl = videoUrl;
            this.currentVideoPoster = i;
            this.showVideo = true;

            // 延迟播放，确保DOM已渲染
            this.$nextTick(() => {
                if (this.$refs.video) {
                    this.$refs.video.play();
                }
            });
        },
        closeVideoBox() {
            this.showVideo = false;
            if (this.$refs.video) {
                this.$refs.video.pause();
                this.$refs.video.currentTime = 0;
            }
            this.currentVideoUrl = "";
            this.currentVideoPoster = "";
        },
        // 视频加载错误处理
        handleVideoError(event) {
            console.error('视频加载失败:', event);
            const video = event.target;
            const error = video.error;

            let errorMessage = '视频加载失败';
            if (error) {
                switch (error.code) {
                    case error.MEDIA_ERR_ABORTED:
                        errorMessage = '视频加载被中断';
                        break;
                    case error.MEDIA_ERR_NETWORK:
                        errorMessage = '网络错误，请检查网络连接';
                        break;
                    case error.MEDIA_ERR_DECODE:
                        errorMessage = '视频解码失败，格式不支持';
                        break;
                    case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                        errorMessage = '视频格式不支持或文件损坏';
                        break;
                    default:
                        errorMessage = '视频播放出现未知错误';
                }
            }

            // 设置错误状态，显示回退界面
            this.videoLoadError = true;
            this.videoErrorMessage = errorMessage;

            // 不显示toast，改为在界面上显示错误信息
            console.log('视频错误信息:', errorMessage);
        },
        // 视频元数据加载完成
        handleVideoLoadedMetadata() {
            console.log('视频元数据加载完成');
            // 元数据加载成功，清除错误状态
            this.videoLoadError = false;
        },
    },
    components: {
        vRadio,
    },
    watch: {
        checkPhotos: {
            // 在vuex离储存提交数据
            handler: function (nv, ov) {
                // console.log('nv', nv);
                const _this = this;
                if (nv && nv.length >= 0) {
                    // hasVal:1=有照片；2:没有
                    const storeData = {
                        checkId: _this.checkId,
                        listId: _this.checkData.listId,
                        checkPhotos: nv.slice(),
                        checkStatus: _this.val,
                        hasVal: nv.length > 0 ? "1" : "2", // 根据实际照片数量设置
                    };

                    // 如果有照片但没有选择是/否，给出提示
                    if (nv.length > 0 && _this.val === null) {
                        // 可以选择在这里给出提示，但不阻止数据提交到Store
                        // _this.$toast.center('请先选择"是"或"否"');
                    }

                    _this.$store.commit("setUploadImgs", storeData);

                    // 验证 Store 中的数据
                    const storeData_verify = _this.$store.getters.getUploadImgs;
                    const currentItem = storeData_verify.find(item =>
                        item.checkId === _this.checkId && item.listId === _this.checkData.listId
                    );
                } else {
                    _this.checkPhotos = [];
                    const storeData = {
                        checkId: _this.checkId,
                        listId: _this.checkData.listId,
                        checkPhotos: [],
                        checkStatus: _this.val,
                        hasVal: "2",
                    };
                    _this.$store.commit("setUploadImgs", storeData);
                }
            },
            immediate: false,
            deep: true,
        },
        // 草稿箱修改
        isModified: {
            handler: function (nv, ov) {
                if (nv) {
                    this.val = this.checkData.checkStatus;
                    if (this.checkData.checkPhotos !== "") {
                        const checkPhotos = JSON.parse(this.checkData.checkPhotos);

                        this.checkPhotos = checkPhotos.map((file) => {
                            // 区分图片和视频：通过文件扩展名判断
                            let fileData;
                            const fileUrl = file.fileUrl || '';
                            const isVideo = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(fileUrl);

                            if (file.imgFileId !== undefined || isVideo) {
                                // 视频文件：使用默认缩略图
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl,
                                    imgFileId: -1, // 视频使用默认缩略图标识
                                    imgFileUrl: defaultVideoThumb
                                };
                            } else {
                                // 图片文件：不设置imgFileId和imgFileUrl
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl
                                    // 图片不设置 imgFileId 和 imgFileUrl
                                };
                            }

                            return fileData;
                        });
                    }
                }
            },
            immediate: true,
        },
        val: {
            handler: function (nv, ov) {
                // console.log('val===', nv);
                const _this = this;
                if (nv === "0") {
                    // 判断否按钮是否可传照片
                    if (
                        _this.checkData.picType === 2 ||
                        _this.checkData.picType === 3
                    ) {
                        _this.showPicList = true;
                        _this.switchRadioGetDataFn(nv);
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: _this.checkPhotos
                                ? _this.checkPhotos.slice()
                                : [],
                            checkStatus: nv,
                            hasVal:
                                !!_this.checkPhotos &&
                                _this.checkPhotos.length > 0
                                    ? "1"
                                    : "2",
                        };
                        _this.$store.commit("setUploadImgs", storeData);
                    } else {
                        _this.showPicList = false;
                        // 不能上传照片，但要更新初始checkStatus === null
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: [],
                            checkStatus: nv,
                            hasVal: "0",
                        };
                        _this.$store.commit("setUploadImgs", storeData);
                    }
                } else if (nv === "1") {
                    // 判断否按钮是否可传照片
                    if (
                        _this.checkData.picType === 1 ||
                        _this.checkData.picType === 3
                    ) {
                        _this.showPicList = true;
                        _this.switchRadioGetDataFn(nv);
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: _this.checkPhotos
                                ? _this.checkPhotos.slice()
                                : [],
                            checkStatus: nv,
                            hasVal:
                                _this.checkPhotos &&
                                _this.checkPhotos.length > 0
                                    ? "1"
                                    : "2",
                        };
                        _this.$store.commit("setUploadImgs", storeData);
                    } else {
                        _this.showPicList = false;
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: [],
                            checkStatus: nv,
                            hasVal: "0",
                        };
                        _this.$store.commit("setUploadImgs", storeData);
                    }
                } else {
                    // checkStatus初次加载值为null
                    _this.showPicList = false;
                    const storeData = {
                        checkId: _this.checkId,
                        listId: _this.checkData.listId,
                        checkPhotos: [],
                        checkStatus: null,
                        hasVal: "2",
                    };
                    _this.$store.commit("setUploadImgs", storeData);
                }
            },
            immediate: true,
        },
    },
};
</script>
<style lang="less" scoped>
@import "../../assets/css/mixin.less";
.check-item {
    .c1 {
        padding: 34px 30px 0 30px;
        font-size: 32px;
        color: #333;
        position: relative;
        &::after {
            .setBottomLine(#ECECEC);
        }
        ul {
            display: flex;
            padding: 28px 0 36px 0;
            li {
                flex: 0 0 188px;
            }
        }
    }
    .c2 {
        h2 {
            font-size: 28px;
            padding: 15px 30px;
            color: #317dff;
            background-color: #f0f7fd;
        }
        ul {
            padding: 36px 30px 42px 30px;
            display: flex;
            position: relative;
            li {
                position: relative;
                flex: 0 0 218px;
                padding-top: 218px;
                &:nth-child(1) {
                    margin-right: 18px;
                }
                &:nth-child(3) {
                    margin-left: 18px;
                }
                .i1 {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 6px;
                    z-index: 10;
                }
                .i1-play {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 20;
                    background: url(../../assets/img/<EMAIL>) no-repeat
                        center center;
                    background-size: 81px 81px;
                }
                video {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 208px;
                    height: 208px;
                }
                span {
                    display: block;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 35px;
                    height: 35px;
                    background: url(../../assets/img/<EMAIL>) no-repeat
                        center center;
                    background-size: 35px 35px;
                    z-index: 30;
                }
                label {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: url("../../assets/img/<EMAIL>") no-repeat
                        center center;
                    background-size: 100% 100%;
                    border-radius: 6px;
                    overflow: hidden;
                    input {
                        width: 1px;
                        height: 1px;
                        overflow: hidden;
                    }
                }
            }
            &::after {
                .setBottomLine(#ECECEC);
            }
        }
    }
    .select-two {
        position: fixed;
        bottom: 0;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        .st-mask {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
        }
        .st-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: calc(100% - 320px);
            padding: 0 160px;
            background: #fff;
            box-shadow: 0 2px 8px #317dff;
            overflow: hidden;
            min-height: 20vh; /* 占屏幕高度的20% */

            /* 移动端适配 */
            @media (max-width: 768px) {
                width: calc(100% - 60px);
                padding: 0 30px;
                min-height: 25vh; /* 移动端占更多高度 */
            }

            @media (max-width: 480px) {
                min-height: 22vh;
            }
            .st-i {
                display: flex;
                justify-content: space-around; /* 确保均匀分布 */
                align-items: center;
                gap: 40px; /* 添加按钮间距 */
                padding: 0 40px; /* 增加左右边距 */
                .st-ic {
                    flex: 1;
                    max-width: 200px; /* 限制最大宽度保持对称 */
                    text-align: center;
                    padding: 100px 0;  /* 进一步增加高度 */
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); /* 添加轻微阴影 */
                    transition: transform 0.2s ease;

                    &:active {
                        transform: scale(0.95); /* 点击时轻微缩放 */
                    }

                    /* 移动端适配 */
                    @media (max-width: 768px) {
                        padding: 80px 10px;  /* 移动端也大幅增加高度 */
                        max-width: 150px;
                    }

                    @media (max-width: 480px) {
                        padding: 60px 10px;  /* 小屏手机适当调整 */
                        max-width: 120px;
                    }
                    label {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        width: 160px;  /* 从140px增加到160px */
                        height: 160px;
                        min-height: 200px; /* 增加最小点击区域 */
                        background: url("../../assets/img/<EMAIL>")
                            no-repeat center center;
                        background-size: 160px auto;  /* 相应增加背景图片尺寸 */
                        overflow: hidden;

                        /* 移动端适配 */
                        @media (max-width: 768px) {
                            width: 120px;
                            height: 120px;
                            background-size: 120px auto;
                            min-height: 160px;
                        }

                        @media (max-width: 480px) {
                            width: 100px;
                            height: 100px;
                            background-size: 100px auto;
                            min-height: 140px;
                        }
                        input {
                            width: 1px;
                            height: 1px;
                            overflow: hidden;
                        }
                    }
                    p {
                        font-size: 42px;  /* 从36px增加到42px */
                        color: #000;
                        font-weight: 600;  /* 加粗字体 */
                        width: auto;
                        text-align: center;
                        padding: 25px 0;  /* 增加文字间距 */
                        margin: 0 auto;

                        /* 移动端适配 */
                        @media (max-width: 768px) {
                            font-size: 36px;
                            padding: 20px 0;
                        }

                        @media (max-width: 480px) {
                            font-size: 32px;
                            padding: 15px 0;
                        }
                    }
                    img {
                        display: block;
                        width: 160px;  /* 从140px增加到160px */
                        height: auto; /* 保持宽高比 */
                        max-width: 100%; /* 响应式 */
                        margin: 0 auto; /* 居中显示 */

                        /* 移动端适配 */
                        @media (max-width: 768px) {
                            width: 120px; /* 手机端适当增大 */
                        }

                        @media (max-width: 480px) {
                            width: 100px; /* 小屏手机适当调整 */
                        }

                        /* 移动端适配 */
                        @media (max-width: 768px) {
                            width: 80px; /* 手机端缩小尺寸 */
                        }

                        @media (max-width: 480px) {
                            width: 60px; /* 小屏手机进一步缩小 */
                        }
                    }
                }
                .st-icrt {
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    min-height: 200px; /* 增加最小点击区域 */

                    img {
                        display: block;
                        margin: 0 auto;
                        width: 160px;  /* 从140px增加到160px */
                        height: auto;

                        /* 移动端适配 */
                        @media (max-width: 768px) {
                            width: 120px; /* 手机端适当增大 */
                        }

                        @media (max-width: 480px) {
                            width: 100px; /* 小屏手机适当调整 */
                        }
                    }
                    p {
                        text-align: center;
                        font-size: 42px;  /* 从36px增加到42px */
                        color: #000;
                        font-weight: 600;  /* 加粗字体 */
                        padding: 25px 0;  /* 增加文字间距 */
                        margin: 0 auto;

                        /* 移动端适配 */
                        @media (max-width: 768px) {
                            font-size: 36px;
                            padding: 20px 0;
                        }

                        @media (max-width: 480px) {
                            font-size: 32px;
                            padding: 15px 0;
                        }
                    }
                }
            }
        }
    }
    .vi {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;

        .vi-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1;
        }

        .vi-content {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;

            .vic-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
            }

            .vi-main {
                position: relative;
                width: 90%;
                max-width: 800px;
                z-index: 2;

                .video-preview-container {
                    position: relative;
                    width: 100%;
                }

                video {
                    width: 100%;
                    height: auto;
                    max-height: 70vh;
                    background: #000;
                    border-radius: 8px;
                    outline: none;
                    display: block;
                    margin: 0 auto;

                    /* 移动端适配 */
                    @media (max-width: 768px) {
                        max-height: 60vh;
                        border-radius: 4px;
                    }
                }

                .video-error-fallback {
                    background: #f5f5f5;
                    border: 2px dashed #ccc;
                    border-radius: 8px;
                    padding: 40px 20px;
                    text-align: center;
                    color: #666;

                    .error-icon {
                        font-size: 48px;
                        margin-bottom: 16px;
                    }

                    p {
                        margin: 8px 0;
                        font-size: 16px;

                        &.error-detail {
                            font-size: 14px;
                            color: #999;
                        }
                    }

                    .download-link {
                        display: inline-block;
                        margin-top: 16px;
                        padding: 8px 16px;
                        background: #317dff;
                        color: white;
                        text-decoration: none;
                        border-radius: 4px;
                        font-size: 14px;

                        &:hover {
                            background: #2563eb;
                        }
                    }

                    /* 移动端适配 */
                    @media (max-width: 768px) {
                        padding: 30px 15px;

                        .error-icon {
                            font-size: 36px;
                        }

                        p {
                            font-size: 14px;

                            &.error-detail {
                                font-size: 12px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
